{
    files = {
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\devices\nvidia\nvidia_common.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\add\nvidia\add_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\causal_softmax\nvidia\causal_softmax_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\clip\nvidia\clip_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\conv\nvidia\conv_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\gemm\nvidia\gemm_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\mul\nvidia\mul_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\rearrange\nvidia\rearrange_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\relu\nvidia\relu_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\rms_norm\nvidia\rms_norm_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\rope\nvidia\rope_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\sin\nvidia\sin_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\sub\nvidia\sub_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\swiglu\nvidia\swiglu_nvidia.cu.obj]],
        [[build\windows\x64\release\infini-utils.lib]]
    },
    values = {
        [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]],
        {
            [[-LC:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib64\stubs]],
            [[-LC:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64]],
            [[-Lbuild\windows\x64\release]],
            "-lcudart",
            "-lcublas",
            "-lcuda",
            "-linfini-utils",
            "-lcudadevrt",
            "-m64",
            "-gencode",
            "arch=compute_89,code=sm_89",
            "-dlink"
        }
    }
}