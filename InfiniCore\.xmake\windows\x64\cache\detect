{
    ["lib.detect.has_flags"] = {
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu___-O3"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-DNDEBUG"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx_cxflags_-nologo_/wd4068"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-Xcompiler=-Wno-error=deprecated-declarations"] = false,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-Xcompiler=/utf-8"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-Xcompiler=/W3"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx_cxflags_-nologo_cl_sourceDependencies"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx_cxflags_-nologo_-DNDEBUG"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-Xcompiler=/WX"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__--allow-unsupported-compiler"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx_cxflags_-nologo_/openmp"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-rdc=true"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-MD -MF"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_culd_culdflags__-gencode arch=compute_89,code=sm_89"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__--expt-relaxed-constexpr"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx_cxxflags_-nologo_/openmp"] = true,
        ["windows_x64_C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe_19.44.35213_cxx__-nologo_-O2"] = true,
        ["windows_x64_C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9_cu_cuflags__-gencode arch=compute_89,code=sm_89"] = true
    },
    find_program_gfortran_arch_x64_plat_windows_checktoolfc = {
        gfortran = false,
        g95 = false
    },
    find_cudadevices = {
        succeed = true,
        data = {
            {
                maxTexture1DLinear = 268435456,
                ["$id"] = 0,
                pciBusID = 1,
                totalConstMem = 65536,
                maxThreadsPerBlock = 1024,
                kernelExecTimeoutEnabled = true,
                memoryClockRate = 8001000,
                ["$flops"] = 2903040000,
                maxTexture2DMipmap = {
                    32768,
                    32768
                },
                l2CacheSize = 33554432,
                maxTexture1DLayered = {
                    32768,
                    2048
                },
                maxSurface2D = {
                    131072,
                    65536
                },
                maxTexture2D = {
                    131072,
                    65536
                },
                regsPerBlock = 65536,
                managedMemory = true,
                sharedMemPerBlockOptin = 101376,
                pciDeviceID = 0,
                asyncEngineCount = 1,
                maxTextureCubemapLayered = {
                    32768,
                    2046
                },
                ECCEnabled = false,
                deviceOverlap = true,
                unifiedAddressing = true,
                name = "NVIDIA GeForce RTX 4060 Laptop GPU",
                memoryBusWidth = 128,
                multiProcessorCount = 24,
                sharedMemPerBlock = 49152,
                integrated = false,
                computePreemptionSupported = true,
                clockRate = 1890000,
                surfaceAlignment = 512,
                regsPerMultiprocessor = 65536,
                computeMode = 0,
                memPitch = 2147483647,
                isMultiGpuBoard = false,
                pageableMemoryAccess = false,
                pageableMemoryAccessUsesHostPageTables = false,
                singleToDoublePrecisionPerfRatio = 64,
                globalL1CacheSupported = true,
                totalGlobalMem = 8585216000,
                maxTexture2DGather = {
                    32768,
                    32768
                },
                maxTexture3D = {
                    16384,
                    16384,
                    16384
                },
                maxThreadsDim = {
                    1024,
                    1024,
                    64
                },
                localL1CacheSupported = true,
                maxThreadsPerMultiProcessor = 1536,
                concurrentManagedAccess = false,
                maxTexture2DLinear = {
                    131072,
                    65000,
                    2097120
                },
                pciDomainID = 0,
                canUseHostPointerForRegisteredMem = false,
                canMapHostMemory = true,
                cooperativeLaunch = true,
                maxTexture1D = 131072,
                maxGridSize = {
                    2147483647,
                    65535,
                    65535
                },
                concurrentKernels = true,
                maxTexture1DMipmap = 32768,
                sharedMemPerMultiprocessor = 102400,
                uuid = "75d4107f-ac55-161a-f93a-d75e7f4a675c",
                maxSurface2DLayered = {
                    32768,
                    32768,
                    2048
                },
                tccDriver = false,
                maxSurfaceCubemapLayered = {
                    32768,
                    2046
                },
                maxTexture3DAlt = {
                    8192,
                    8192,
                    32768
                },
                maxSurface1DLayered = {
                    32768,
                    2048
                },
                streamPrioritiesSupported = true,
                texturePitchAlignment = 32,
                major = 8,
                luid = "fdbd530f00000000",
                minor = 9,
                directManagedMemAccessFromHost = false,
                maxTextureCubemap = 32768,
                textureAlignment = 512,
                multiGpuBoardGroupID = 0,
                maxSurface1D = 32768,
                warpSize = 32,
                luidDeviceNodeMask = 1,
                maxTexture2DLayered = {
                    32768,
                    32768,
                    2048
                },
                cooperativeMultiDeviceLaunch = false,
                maxSurfaceCubemap = 32768,
                maxSurface3D = {
                    16384,
                    16384,
                    16384
                }
            }
        }
    },
    ["detect.sdks.find_cuda"] = {
        cuda = {
            includedirs = {
                [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include]]
            },
            msbuildextensionsdir = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\extras\visual_studio_integration\MSBuildExtensions]],
            linkdirs = {
                [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64]]
            },
            sdkdir = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9]],
            version = "12.9",
            bindir = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin]]
        }
    },
    find_program_msvc_arch_x64_plat_windows_checktoolar = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    find_program_cuda_arch_x64_plat_windows_checktoolcxx = {
        ["nvc++"] = false
    },
    find_program_cuda_arch_x64_plat_windows_checktoolfc = {
        nvfortran = false
    },
    find_programver = {
        ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe"] = "19.44.35213",
        ["C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc"] = "12.9"
    },
    ["core.tools.nvcc.has_flags"] = {
        ["C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc_12.9"] = {
            ["--compile-as-tools-patch"] = true,
            ["--forward-slash-prefix-opts"] = true,
            ["--device-link"] = true,
            ["--drive-prefix"] = true,
            ["--force-cl-env-setup"] = true,
            ["--Wdefault-stream-launch"] = true,
            ["-This"] = true,
            ["--extended-lambda"] = true,
            ["--dependency-drive-prefix"] = true,
            ["-forward-unknown-to-host-compiler"] = true,
            ["--optimization-info"] = true,
            ["--library-path"] = true,
            ["-arch"] = true,
            ["--Wno-deprecated-gpu-targets"] = true,
            ["--frandom-seed"] = true,
            ["--forward-unknown-opts"] = true,
            ["--restrict"] = true,
            ["--ftemplate-backtrace-limit"] = true,
            ["-lineinfo"] = true,
            ["--default-stream"] = true,
            ["--clean-targets"] = true,
            ["--lto"] = true,
            ["--define-macro"] = true,
            ["-forward-slash-prefix-opts"] = true,
            ["--profile"] = true,
            ["--input-drive-prefix"] = true,
            ["-dc"] = true,
            ["--Wext-lambda-captures-this"] = true,
            ["--output-directory"] = true,
            ["--lib"] = true,
            ["--target-directory"] = true,
            ["--optimize"] = true,
            ["--dryrun"] = true,
            ["--help"] = true,
            ["--linker-options"] = true,
            ["--Wreorder"] = true,
            ["--compile"] = true,
            ["-gencode"] = true,
            ["--cuda"] = true,
            ["--archiver-binary"] = true,
            ["--library"] = true,
            ["--allow-unsupported-compiler"] = true,
            ["--ftz"] = true,
            ["--undefine-macro"] = true,
            ["--keep-dir"] = true,
            ["-dlink"] = true,
            ["--machine"] = true,
            ["-dlto"] = true,
            ["--libdevice-directory"] = true,
            ["--generate-dependencies"] = true,
            ["--forward-unknown-to-host-linker"] = true,
            ["--gpu-architecture"] = true,
            ["--prec-sqrt"] = true,
            ["--version"] = true,
            ["--include-path"] = true,
            ["-frandom-seed"] = true,
            ["--threads"] = true,
            ["--keep-device-functions"] = true,
            ["--system-include"] = true,
            ["--save-temps"] = true,
            ["--fatbin"] = true,
            ["--x"] = true,
            ["--fmad"] = true,
            ["--display-error-number"] = true,
            ["--use-local-env"] = true,
            ["--device-w"] = true,
            ["--cubin"] = true,
            ["--ltoir"] = true,
            ["-o"] = true,
            ["--brief-diagnostics"] = true,
            ["--split-compile-extended"] = true,
            ["--generate-code"] = true,
            ["--ptx"] = true,
            ["--debug"] = true,
            ["-forward-unknown-opts"] = true,
            ["--compiler-bindir"] = true,
            ["--no-device-link"] = true,
            ["--run-args"] = true,
            ["--diag-suppress"] = true,
            ["--preprocess"] = true,
            ["--Ofast-compile"] = true,
            ["--cudadevrt"] = true,
            ["--list-gpu-code"] = true,
            ["--list-gpu-arch"] = true,
            ["--cudart"] = true,
            ["--diag-error"] = true,
            ["--optix-ir"] = true,
            ["--compiler-options"] = true,
            ["--shared"] = true,
            ["--keep"] = true,
            ["--device-debug"] = true,
            ["--no-display-error-number"] = true,
            ["--pre-include"] = true,
            ["--generate-dependency-targets"] = true,
            ["--entries"] = true,
            ["--ptxas-options"] = true,
            ["--device-stack-protector"] = true,
            ["--dlink-time-opt"] = true,
            ["--expt-extended-lambda"] = true,
            ["-forward-unknown-to-host-linker"] = true,
            ["--dependency-target-name"] = true,
            ["--output-file"] = true,
            ["--options-file"] = true,
            ["--generate-line-info"] = true,
            ["--forward-unknown-to-host-compiler"] = true,
            ["--link"] = true,
            ["--static-global-template-stub"] = true,
            ["--prec-div"] = true,
            ["--Werror"] = true,
            ["-ccbin"] = true,
            ["--m64"] = true,
            ["--gpu-code"] = true,
            ["--dopt"] = true,
            ["-Turning"] = true,
            ["--source-in-ptx"] = true,
            ["--archive-options"] = true,
            ["-bar"] = true,
            ["--extensible-whole-program"] = true,
            ["--Wno-deprecated-declarations"] = true,
            ["--jump-table-density"] = true,
            ["--objdir-as-tempdir"] = true,
            ["--diag-warn"] = true,
            ["-dw"] = true,
            ["--gen-opt-lto"] = true,
            ["-MF"] = true,
            ["--relocatable-device-code"] = true,
            ["--no-compress"] = true,
            ["--device-entity-has-hidden-visibility"] = true,
            ["--time"] = true,
            ["--relocatable-ptx"] = true,
            ["--extra-device-vectorization"] = true,
            ["--no-host-device-initializer-list"] = true,
            ["--no-exceptions"] = true,
            ["--generate-dependencies-with-compile"] = true,
            ["--split-compile"] = true,
            ["--no-align-double"] = true,
            ["--Wmissing-launch-bounds"] = true,
            ["--ftemplate-depth"] = true,
            ["--nvlink-options"] = true,
            ["--std"] = true,
            ["--verbose"] = true,
            ["--fdevice-syntax-only"] = true,
            ["--compress-mode"] = true,
            ["--expt-relaxed-constexpr"] = true,
            ["--run"] = true,
            ["--dependency-output"] = true,
            ["--disable-warnings"] = true,
            ["--resource-usage"] = true,
            ["--device-c"] = true,
            ["--no-host-device-move-forward"] = true,
            ["-code"] = true,
            ["--maxrregcount"] = true,
            ["--fdevice-time-trace"] = true,
            ["--dont-use-profile"] = true
        }
    },
    find_program_msvc_arch_x64_plat_windows_checktoolcxx = {
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    },
    find_program_msvc_arch_x64_plat_windows_checktoolld = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    find_program_cuda_arch_x64_plat_windows_checktoolcu = {
        nvcc = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]]
    },
    find_program = {
        git = [[D:\Program Files\Git\cmd\git.exe]],
        nim = false,
        ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        ["C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin\\nvcc"] = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]],
        ["7z"] = [[C:\Program Files\xmake\winenv\bin\7z]],
        nvcc = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]],
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]],
        gzip = false
    },
    find_program_cuda_arch_x64_plat_windows_checktoolculd = {
        nvcc = [[C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc]]
    },
    find_program_cuda_arch_x64_plat_windows_checktoolfcld = {
        nvfortran = false
    },
    find_program_gfortran_arch_x64_plat_windows_checktoolfcld = {
        gfortran = false,
        g95 = false
    },
    find_program_msvc_arch_x64_plat_windows_checktoolsh = {
        ["link.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]]
    },
    find_program_msvc_arch_x64_plat_windows_checktoolcc = {
        ["cl.exe"] = [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\cl.exe]]
    }
}