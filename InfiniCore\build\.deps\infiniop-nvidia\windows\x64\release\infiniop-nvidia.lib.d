{
    files = {
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\devices\nvidia\nvidia_common.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\add\nvidia\add_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\causal_softmax\nvidia\causal_softmax_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\clip\nvidia\clip_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\conv\nvidia\conv_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\gemm\nvidia\gemm_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\mul\nvidia\mul_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\rearrange\nvidia\rearrange_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\relu\nvidia\relu_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\rms_norm\nvidia\rms_norm_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\rope\nvidia\rope_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\sin\nvidia\sin_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\sub\nvidia\sub_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\src\infiniop\ops\swiglu\nvidia\swiglu_nvidia.cu.obj]],
        [[build\.objs\infiniop-nvidia\windows\x64\release\rules\cuda\devlink\infiniop-nvidia_gpucode.cu.obj]],
        [[build\windows\x64\release\infini-utils.lib]]
    },
    values = {
        [[C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64\link.exe]],
        {
            "-nologo",
            "-machine:x64",
            "/opt:ref",
            "/opt:icf"
        }
    }
}